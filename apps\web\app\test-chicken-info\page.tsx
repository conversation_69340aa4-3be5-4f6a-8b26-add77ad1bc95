"use client";

import { useConnectedWalletChickenInfo } from "@/features/chickens";
import { useStateContext } from "@/providers/app/state";
import { useEffect, useState } from "react";

export default function TestChickenInfoPage() {
  const { address, isConnected } = useStateContext();
  const [isHydrated, setIsHydrated] = useState(false);

  const chickenInfoResponse = useConnectedWalletChickenInfo({
    includeMetadata: false,
    includeGenes: false, // Skip genes for faster testing
    includeBattleStats: false,
    includeBreedCount: false,
    includeCooldown: false,
    includeRentalStatus: false,
  });

  // Handle hydration
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Show loading state during hydration
  if (!isHydrated) {
    return (
      <div className="container mx-auto p-8 text-white">
        <h1 className="text-2xl font-bold mb-4 text-white">
          Chicken Info Test
        </h1>
        <p className="text-gray-300">Loading...</p>
      </div>
    );
  }

  if (!isConnected) {
    return (
      <div className="container mx-auto p-8 text-white">
        <h1 className="text-2xl font-bold mb-4 text-white">
          Chicken Info Test
        </h1>
        <p className="text-gray-300">
          Please connect your wallet to test the chicken info fetcher.
        </p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-8 text-white">
      <h1 className="text-2xl font-bold mb-4 text-white">Chicken Info Test</h1>
      <p className="mb-4 text-gray-300">Connected Address: {address}</p>

      {chickenInfoResponse.isLoading && (
        <div className="mb-4">
          <p className="text-gray-300">Loading chicken information...</p>
        </div>
      )}

      {chickenInfoResponse.error && (
        <div className="mb-4 p-4 bg-red-900/20 border border-red-500 text-red-400 rounded">
          <p>Error: {chickenInfoResponse.error.message}</p>
        </div>
      )}

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2 text-white">Summary</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="p-4 bg-blue-900/30 border border-blue-500/30 rounded">
            <p className="font-semibold text-blue-300">Total Chickens</p>
            <p className="text-2xl text-white">
              {chickenInfoResponse.totalCount}
            </p>
          </div>
          <div className="p-4 bg-green-900/30 border border-green-500/30 rounded">
            <p className="font-semibold text-green-300">Ordinary</p>
            <p className="text-2xl text-white">
              {chickenInfoResponse.chickensByType.ordinary.length}
            </p>
          </div>
          <div className="p-4 bg-yellow-900/30 border border-yellow-500/30 rounded">
            <p className="font-semibold text-yellow-300">Legacy</p>
            <p className="text-2xl text-white">
              {chickenInfoResponse.chickensByType.legacy.length}
            </p>
          </div>
          <div className="p-4 bg-purple-900/30 border border-purple-500/30 rounded">
            <p className="font-semibold text-purple-300">Genesis</p>
            <p className="text-2xl text-white">
              {chickenInfoResponse.chickensByType.genesis.length}
            </p>
          </div>
        </div>
      </div>

      {chickenInfoResponse.chickens.length > 0 && (
        <div>
          <h2 className="text-xl font-semibold mb-4 text-white">Chickens</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {chickenInfoResponse.chickens.slice(0, 6).map((chicken) => (
              <div
                key={chicken.tokenId}
                className="border border-gray-600 rounded-lg p-4 bg-gray-800/50 shadow"
              >
                <div className="flex items-center mb-2">
                  <img
                    src={chicken.image}
                    alt={`Chicken ${chicken.tokenId}`}
                    className="w-16 h-16 rounded mr-3"
                    onError={(e) => {
                      e.currentTarget.src = `https://chicken-api-ivory.vercel.app/api/image/${chicken.tokenId}.png`;
                    }}
                  />
                  <div>
                    <h3 className="font-semibold text-white">
                      #{chicken.tokenId}
                    </h3>
                    <p className="text-sm text-gray-400">{chicken.type}</p>
                  </div>
                </div>

                <div className="space-y-1 text-sm text-gray-300">
                  <p>
                    <span className="font-medium text-gray-200">Level:</span>{" "}
                    {chicken.level}
                  </p>
                  <p>
                    <span className="font-medium text-gray-200">
                      Daily Feathers:
                    </span>{" "}
                    {chicken.dailyFeathers}
                  </p>
                  <p>
                    <span className="font-medium text-gray-200">
                      Breed Count:
                    </span>{" "}
                    {chicken.breedCount}
                  </p>
                  <p>
                    <span className="font-medium text-gray-200">Win Rate:</span>{" "}
                    {chicken.winRate}%
                  </p>
                  <p>
                    <span className="font-medium text-gray-200">
                      Available:
                    </span>{" "}
                    <span
                      className={
                        chicken.isAvailable ? "text-green-400" : "text-red-400"
                      }
                    >
                      {chicken.isAvailable ? "Yes" : "No"}
                    </span>
                  </p>
                  {chicken.rentalStatus && (
                    <p>
                      <span className="font-medium text-gray-200">Status:</span>{" "}
                      {chicken.rentalStatus.statusLabel}
                    </p>
                  )}
                  {chicken.battleStats && (
                    <p>
                      <span className="font-medium text-gray-200">
                        Battle State:
                      </span>{" "}
                      {chicken.battleStats.state || "normal"}
                    </p>
                  )}
                  {chicken.isOnCooldown && (
                    <p className="text-orange-400">
                      <span className="font-medium">On Cooldown</span>
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>

          {chickenInfoResponse.chickens.length > 6 && (
            <p className="mt-4 text-gray-400">
              Showing 6 of {chickenInfoResponse.chickens.length} chickens
            </p>
          )}
        </div>
      )}

      {!chickenInfoResponse.isLoading &&
        chickenInfoResponse.chickens.length === 0 && (
          <div className="text-center py-8">
            <p className="text-gray-600">No chickens found for this address.</p>
          </div>
        )}
    </div>
  );
}
